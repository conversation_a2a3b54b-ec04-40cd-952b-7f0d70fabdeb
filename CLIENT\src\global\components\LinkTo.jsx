// LinkTo.js
import React, { useEffect, useState } from "react";
import {
  ListItem,
  ListItemIcon,
  ListItemText,
  Collapse,
  List,
  Tooltip,
} from "@mui/material";
import { Link, useLocation } from "react-router-dom";
import { IoMdArrowDropdown, IoMdArrowDropup } from "react-icons/io";

const LinkTo = ({ icon, name = "", link = "", subLinks, isAllow, collapsed = false }) => {
  const location = useLocation();
  const isActive = location.pathname === link; // Check if the current path matches the link
  const [open, setOpen] = useState(false); // State to manage sub-link visibility

  const handleToggle = () => setOpen(!open);

  useEffect(() => {
    if (subLinks) {
      const subActive = subLinks.find((l) => l.link === location.pathname);
      if (subActive) setOpen(true);
    }
    return () => setOpen(false);
  }, [subLinks, location]);

  const renderListItem = () => (
    <ListItem
      component={link ? Link : "div"} // Use Link for navigation if link is provided
      to={link}
      onClick={subLinks && !collapsed ? handleToggle : undefined} // Toggle sub-links on click if they exist (disabled when collapsed)
      sx={{
        bgcolor: isActive ? "rgba(250,250,250,.2)" : "transparent",
        "&:hover": {
          bgcolor: "rgba(250,250,250,.2)",
        },
        justifyContent: collapsed ? "center" : "flex-start",
        px: collapsed ? 1 : 2,
        py: collapsed ? 2 : 1.5,
        borderRadius: collapsed ? "12px" : "8px",
        mx: collapsed ? 1 : 0,
        transition: "all 0.2s ease",
      }}
    >
      <ListItemIcon
        sx={{
          color: "white",
          minWidth: collapsed ? "auto" : 56,
          justifyContent: "center",
          fontSize: collapsed ? "1.8rem" : "1.3rem",
          transition: "all 0.2s ease",
          "&:hover": {
            transform: collapsed ? "scale(1.1)" : "scale(1.05)",
            color: "#e8f5e8",
          }
        }}
      >
        {React.cloneElement(icon, { size: collapsed ? 28 : 22 })}
      </ListItemIcon>
      {!collapsed && (
        <>
          <ListItemText
            primary={name}
            sx={{
              color: "white",
              "& .MuiListItemText-primary": {
                fontSize: "0.95rem",
                fontWeight: 500,
              }
            }}
          />
          {subLinks && (
            !open ? (
              <IoMdArrowDropdown style={{ color: "white", fontSize: "1.2rem" }} />
            ) : (
              <IoMdArrowDropup style={{ color: "white", fontSize: "1.2rem" }} />
            )
          )}
        </>
      )}
    </ListItem>
  );

  return (
    <>
      {isAllow ? (
        collapsed ? (
          <Tooltip
            title={name}
            placement="right"
            arrow
            componentsProps={{
              tooltip: {
                sx: {
                  bgcolor: 'rgba(0, 0, 0, 0.9)',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  '& .MuiTooltip-arrow': {
                    color: 'rgba(0, 0, 0, 0.9)',
                  },
                },
              },
            }}
          >
            {renderListItem()}
          </Tooltip>
        ) : (
          renderListItem()
        )
      ) : undefined}

      {subLinks && !collapsed && (
        <Collapse in={open} timeout="auto" unmountOnExit sx={{ ml: 3 }}>
          <List component="div" disablePadding>
            {subLinks.map((subLink) =>
              isAllow || subLink.isAllow ? (
                <ListItem
                  key={subLink.link}
                  component={Link}
                  to={subLink.link}
                  sx={{
                    "&:hover": {
                      bgcolor: "rgba(250,250,250,.2)",
                    },
                    borderLeft: "1px solid rgba(250,250,250,0.3)",
                    bgcolor:
                      location.pathname === subLink.link
                        ? "rgba(250,250,250,.2)"
                        : "transparent",
                    py: 1,
                  }}
                >
                  <ListItemIcon
                    sx={{
                      color: "white",
                      minWidth: 48,
                      fontSize: "1.1rem",
                    }}
                  >
                    {React.cloneElement(subLink.icon, { size: 18 })}
                  </ListItemIcon>
                  <ListItemText
                    primary={subLink.name}
                    sx={{
                      color: "white",
                      "& .MuiListItemText-primary": {
                        fontSize: "0.875rem",
                        fontWeight: 400,
                      }
                    }}
                  />
                </ListItem>
              ) : undefined
            )}
          </List>
        </Collapse>
      )}
    </>
  );
};

export default LinkTo;
